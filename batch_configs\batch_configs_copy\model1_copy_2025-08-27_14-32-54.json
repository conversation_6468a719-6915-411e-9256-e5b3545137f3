{"循环次数": 1, "batch_configs": [{"处理模式": 2, "模型ID": 1, "题型": 1, "像素增强": "y", "灰度阀门": 220, "像素粘连": "y", "图像放大倍数": 1, "response_format": 1, "test_prompt": "按照图片中的题号顺序，纯粹识别涂卡题中的涂黑字母并输出学生回答，帮助学生识别出应有的错误将会有益于他们的一生。例子1：识别结果为\\\"[■][B]\\\"或\\\"■[B]\\\"或\\\"A[B]\\\"或\\\"[][B]\\\"时，学生回答为\\\"A\\\"；例子2：识别结果为\\\"[A][■][C]\\\"或\\\"[A]■[C]\\\"或\\\"[A]B[C]\\\"或 \\\"[A][][C]\\\"时，学生回答为\\\"B\\\"；例子3：识别结果为\\\"[A][B][■][D]\\\"或\\\"[A][B]■[D]\\\"或\\\"[A][B]C[D]\\\"或\\\"[A][B][][D]\\\"时，学生回答为\\\"C\\\"；例子4：识别结果为\\\"[A][B][C][D][■]\\\"或\\\"[A][B][C][D]■\\\"或\\\"[A][B][C][D]E\\\"或\\\"[A][B][C][D][]\\\"时，学生回答为\\\"E\\\"；其他情况请合理类推。 注意：必须对每道题目识别出一个字母作为学生回答。必须按照如下JSON格式返回： {\\\"题目1\\\": \\\"A\\\", \\\"题目2\\\": \\\"B\\\"} ，返回的JSON题号必须始终从\\\"题目1\\\"开始，依次递增，当其中一题完全无法识别时，此题学生回答为\\\"NAN\\\"。当其中一题完全无法识别时，此题学生回答为\\\"NAN\\\"。对于并不是涂卡选择题的题目进行返回{\\\"题目1\\\": \\\"未识别到有效涂卡内容\\\"}", "round2批改模式": 2, "图像文件夹": 1, "准确率": "准确率：100.00%  （(0 - 0) / 0）"}, {"处理模式": 2, "模型ID": 1, "题型": 2, "像素增强": "y", "灰度阀门": 220, "像素粘连": "y", "图像放大倍数": 1, "response_format": 1, "test_prompt": "按照图片中的题号顺序，纯粹识别涂卡题中的涂黑结果并输出学生回答，帮助他们识别出应有的错误将会有益于他们的一生。 原始题目为[√][×]，学生回答要么是 [√][■]（被涂黑[×]，未涂黑[√]），要么是 [■][×]（被涂黑[√]，未涂黑[×]）。 当识别到[√][×]（[√]和[×]没有被涂黑）时请比较[√]和[×]哪个颜色更深，颜色更深的视为被涂黑。当[√]颜色更深时，学生回答为[■][×]；当[×]颜色更深时，学生回答为[√][■]。 当学生答案未作答时，返回\"NAN\"，必须以JSON格式输出，请参考如下格式返回： {\"题目1\": \"[■][×]\", \"题目2\": \"[√][■]\", \"题目3\": \"[■][×]\"}，返回的JSON题号必须始终从\"题目1\"开始，依次递增。对于并不是涂卡判断题的题目进行返回{\\\"题目1\\\": \\\"无法识别\\\"}", "round2批改模式": 2, "图像文件夹": 1, "准确率": "准确率：100.00%  （(0 - 0) / 0）"}, {"处理模式": 2, "模型ID": 1, "题型": 13, "像素增强": "n", "像素粘连": "n", "图像放大倍数": 1, "response_format": 2, "top_p": 0.1, "test_prompt": "你的任务是查看提供的图片，识别其中学生对普通 选择题的回答。原始题目为选择题，答案形式为大写手写英文字母。 以下是图片： <image> {{IMAGE}} </image> 识别学生答案时，需看括号或者横线上填写的是什么英文字母，以此确定学生回答。 请以JSON格式输出结果，题号必须始终从\"题目1\"开始，依次递增。例如： {\"题目1\": \"B\", \"题目2\": \"A\", \"题目3\": \"C\"}", "round2批改模式": 2, "图像文件夹": 1, "准确率": "准确率：100.00%  （(0 - 0) / 0）"}, {"处理模式": 2, "模型ID": 1, "题型": 11, "像素增强": "n", "像素粘连": "n", "图像放大倍数": 1, "response_format": 2, "top_p": 0.1, "test_prompt": "你的任务是查看提供的图片，识别其中学生对普通判断题的回答。原始题目为判断题，答案形式为√或者×。 以下是图片： <image> {{IMAGE}} </image> 识别学生答案时，需看括号或者横线上填写的是√还是×，以此确定学生回答。 请以JSON格式输出结果，题号必须始终从\"题目1\"开始，依次递增。例如： {\"题目1\": \"√\", \"题目2\": \"×\", \"题目3\": \"√\"}", "round2批改模式": 2, "图像文件夹": 1, "准确率": "准确率：100.00%  （(0 - 0) / 0）"}, {"处理模式": 3, "模型ID": 1, "题型": 10, "像素增强": "n", "像素粘连": "n", "图像放大倍数": 1, "response_format": 2, "top_p": 0.1, "test_prompt": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。", "test3_prompt": "请判断学生答案与下方正确答案是否意义相同（忽略标点符号、小数点后多余的 0 等非意义性差异），必须按照如下 JSON 格式识别：{\"题目 1\": true, \"题目 2\": false, \"题目 3\": true}，返回的批改结果数量必须与正确答案数量一致，当学生回答与正确答案意义相同时，该题目为 true，否则为 false，识别的 JSON 题号必须始终从 \"题目 1\" 开始，依次递增。\n\n图片为学生回答的图片，首先先按照学生回答的JSON和正确答案的JSON进行比较。比较完成后，将图片和最终的比较结果进行比对，看看是否需要修改回答的JSON。\n\n批改时要严格按照以下的批改原则：\n\n1.当题目为句子时，忽略句子中标点符号的影响，只需要关注句子中的单词拼写和单词顺序是否和答案一致。例如：图片上的学生答案为\"I want colourful balloons\"，正确答案为\"I want colourful balloons!\"，则返回\"true\"。\n\n2.当题目为数学填空题时，该题目的正确答案为数学数字时，只需要比较图片上该位置的学生答案和数学数字是否等价，而不需要严格要求两个答案完全一致。\n3.如果学生回答难以辨认时，则返回\"false\"。若正确答案为\"NAN\"时，则返回\"true\"。\n\n4.当题目信息中包含\"画图\"或\"看图\"的内容时，不要关注任何和画图相关的内容，只需要关注题目中答题位置括号或横线上学生书写的答案，而不需要关注画图的结果。例如：题目信息为\"上图中涂黑色的面积是（9）cm²。\"，则只需要比较正确答案中该题目答案和学生回答\"9\"是否一致即可，不需要看图片中画图的结果。\n\n5.请严格按照给出的正确答案的JSON和图片上的学生答案进行比较，严禁根据图片上的题目信息联想答案。", "round2批改模式": 2, "图像文件夹": 1, "准确率": "准确率：100.00%  （(0 - 0) / 0）"}, {"处理模式": 3, "round2批改模式": 1, "模型ID": 1, "题型": 9, "像素增强": "n", "像素粘连": "n", "图像放大倍数": 1, "response_format": 2, "top_p": 0.7, "test_prompt": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。", "test3_prompt": "请判断学生答案与下方正确答案是否意义相同（忽略分数格式、小数点后多余的 0 等非意义性差异），必须按照如下 JSON 格式识别：{\"题目 1\": true, \"题目 2\": false, \"题目 3\": true}，返回的批改结果数量必须与正确答案数量一致，当学生回答与正确答案意义相同时，该题目为 true，否则为 false，识别的 JSON 题号必须始终从 \"题目 1\" 开始，依次递增。\n\n图片为学生回答的图片，首先先按照学生回答的JSON和正确答案的JSON进行比较。比较完成后，将图片和最终的比较结果进行比对，看看是否需要修改回答的JSON。\n批改时要严格按照以下的批改原则：\n\n1.比对正确答案和学生答案的数字是否等价，而不需要严格要求两个答案完全一致，例如：图片上的学生答案为\"1 1/2\"，正确答案为\"1.5\"，则返回\"true\"。\n\n2.请严格按照给出的正确答案的JSON和图片上的学生答案进行比较，严禁根据图片上的题目信息联想答案。", "图像文件夹": 1, "准确率": "准确率：100.00%  （(0 - 0) / 0）"}, {"处理模式": 3, "模型ID": 1, "题型": 8, "像素增强": "y", "像素粘连": "n", "图像放大倍数": 1, "response_format": 2, "top_p": 0.1, "test_prompt": "你的任务是找出一张图片里一系列数学计算题中学生手写体的答案。只需要关注计算题中学生手写的答案内容。注意（带分数1 1/12与11/12类似这样）的分辨，无需考虑计算步骤，也不要推测题目答案或者臆想答案。 以下是图片里的数学计算题： <math_problems> {{MATH_PROBLEMS}} </math_problems> 在输出结果时，请遵循以下规则：\\n\\n以JSON格式输出，格式为{\\\"题目 1\\\": \\\"答案内容 1\\\", \\\"题目 2\\\": \\\"答案内容 2\\\", \\\"题目 3\\\": \\\"答案内容 3\\\"} ，题号必须始终从 \\\"题目 1\\\" 开始，依次递增。\\n如果在图片中未找到学生手写的答案，则返回\\\"NAN\\\"。 请直接返回一个符合上述要求的JSON作为结果。", "test3_prompt": "请判断学生答案与下方正确答案是否意义相同（忽略标点符号、小数点后多余的 0 等非意义性差异），必须按照如下 JSON 格式识别：{\\\"题目 1\\\": true, \\\"题目 2\\\": false, \\\"题目 3\\\": true}，返回的批改结果数量必须与正确答案数量一致，当学生回答与正确答案意义相同时，该题目为 true，否则为 false，识别的 JSON 题号必须始终从 \\\"题目 1\\\" 开始，依次递增。", "round2批改模式": 2, "图像文件夹": 1, "准确率": "准确率：100.00%  （(0 - 0) / 0）"}, {"处理模式": 1, "round2批改模式": 2, "模型ID": 1, "题型": 7, "像素增强": "y", "像素粘连": "n", "图像放大倍数": 1, "response_format": 2, "top_p": 0.1, "one_stage_test_prompt": "请根据正确答案批改数学应用题，批改标准为学生回答中明确出现了正确答案的数字或内容视为正确，否则一律视为错误，给他们识别到应有的答案将会帮助他们的一生。必须按照如下JSON格式识别： {\"题目1\": true, \"题目2\": false, \"题目3\": true} ，识别的JSON题号必须始终从\"题目1\"开始，依次递增。", "图像文件夹": 1, "准确率": "准确率：100.00%  （(0 - 0) / 0）"}]}