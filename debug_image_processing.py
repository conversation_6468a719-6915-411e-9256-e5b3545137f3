#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import traceback
from image_utils import image_to_base64

def test_single_image():
    """测试单张图片的处理"""
    # 获取第一张图片
    image_dir = "types/tukaxuanzeti/images"
    if not os.path.exists(image_dir):
        print(f"错误：图片目录不存在: {image_dir}")
        return False
    
    # 获取第一张jpg图片
    image_files = [f for f in os.listdir(image_dir) if f.lower().endswith('.jpg')]
    if not image_files:
        print(f"错误：在 {image_dir} 中没有找到jpg图片")
        return False
    
    test_image = os.path.join(image_dir, image_files[0])
    print(f"测试图片: {test_image}")
    
    # 检查文件是否存在
    if not os.path.exists(test_image):
        print(f"错误：图片文件不存在: {test_image}")
        return False
    
    # 检查文件大小
    file_size = os.path.getsize(test_image)
    print(f"文件大小: {file_size} bytes")
    
    if file_size == 0:
        print("错误：图片文件为空")
        return False
    
    # 测试不同的处理参数
    test_cases = [
        {"use_enhance": False, "enhance_threshold": 220, "scale": 1.0, "use_pixel_connection": False},
        {"use_enhance": True, "enhance_threshold": 220, "scale": 1.0, "use_pixel_connection": False},
        {"use_enhance": True, "enhance_threshold": 220, "scale": 1.0, "use_pixel_connection": True},
    ]
    
    for i, params in enumerate(test_cases):
        print(f"\n--- 测试用例 {i+1}: {params} ---")
        try:
            base64_result = image_to_base64(
                test_image,
                use_enhance=params["use_enhance"],
                enhance_threshold=params["enhance_threshold"],
                scale=params["scale"],
                use_pixel_connection=params["use_pixel_connection"]
            )
            
            if base64_result and base64_result.startswith("data:image/"):
                print(f"✓ 成功处理，base64长度: {len(base64_result)}")
                return True
            else:
                print(f"✗ 处理失败，返回值无效: {type(base64_result)}")
                
        except Exception as e:
            print(f"✗ 处理失败，异常: {str(e)}")
            print(f"异常类型: {type(e).__name__}")
            print("详细错误信息:")
            traceback.print_exc()
    
    return False

def test_file_reading():
    """测试基本的文件读取"""
    image_dir = "types/tukaxuanzeti/images"
    image_files = [f for f in os.listdir(image_dir) if f.lower().endswith('.jpg')]
    test_image = os.path.join(image_dir, image_files[0])
    
    print(f"\n--- 测试基本文件读取 ---")
    try:
        with open(test_image, "rb") as f:
            data = f.read()
        print(f"✓ 成功读取文件，大小: {len(data)} bytes")
        
        # 测试PIL读取
        from PIL import Image
        import io
        
        image = Image.open(io.BytesIO(data))
        print(f"✓ PIL成功打开图片，尺寸: {image.size}, 模式: {image.mode}")
        
        return True
        
    except Exception as e:
        print(f"✗ 文件读取失败: {str(e)}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=== 图片处理诊断工具 ===")
    
    # 检查当前工作目录
    print(f"当前工作目录: {os.getcwd()}")
    
    # 测试基本文件读取
    if not test_file_reading():
        print("基本文件读取失败，退出")
        sys.exit(1)
    
    # 测试图片处理
    if test_single_image():
        print("\n✓ 图片处理测试通过")
    else:
        print("\n✗ 图片处理测试失败")
        sys.exit(1)
