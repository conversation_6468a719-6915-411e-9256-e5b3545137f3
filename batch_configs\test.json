{"循环次数": 1, "batch_configs": [{"处理模式": 1, "题型": 13, "模型ID": 2, "图像文件夹": 1, "response_format": 1, "temperature": 0.7, "top_p": 0.9, "max_tokens": 4096}, {"处理模式": 1, "题型": 13, "模型ID": 2, "图像文件夹": 1, "response_format": 1, "temperature": 0.7, "top_p": 0.9, "max_tokens": 4096, "one_stage_test_system_prompt": "one_stage_test_system_prompt.md", "one_stage_test_user_prompt": "n"}, {"处理模式": 2, "题型": 13, "模型ID": 2, "图像文件夹": 1, "response_format": 1, "round2批改模式": 2, "temperature": 0.7, "top_p": 0.9, "max_tokens": 4096}, {"处理模式": 2, "题型": 13, "模型ID": 2, "图像文件夹": 1, "response_format": 1, "round2批改模式": 2, "temperature": 0.7, "top_p": 0.9, "max_tokens": 4096, "test_system_prompt": "test_system_prompt.md", "test2_system_prompt": "test2_system_prompt.md", "test_user_prompt": "n", "test2_user_prompt": "n"}, {"处理模式": 3, "题型": 13, "模型ID": 2, "图像文件夹": 1, "response_format": 1, "temperature": 0.7, "top_p": 0.9, "max_tokens": 4096}, {"处理模式": 3, "题型": 13, "模型ID": 2, "图像文件夹": 1, "response_format": 1, "temperature": 0.7, "top_p": 0.9, "max_tokens": 4096, "test_system_prompt": "test_system_prompt.md", "test3_system_prompt": "test3_system_prompt.md", "test_user_prompt": "n", "test3_user_prompt": "n"}]}